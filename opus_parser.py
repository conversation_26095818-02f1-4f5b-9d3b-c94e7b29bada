import struct
import os

def read_opus_file(file_path):
    """
    读取opus文件，解析4字节长度+数据的格式
    返回解析出的数据列表
    """
    data_chunks = []
    with open(file_path, 'rb') as f:
        while True:
            # 读取4字节长度
            length_bytes = f.read(4)
            if not length_bytes or len(length_bytes) < 4:
                break
                
            # 解析长度（使用小端序）
            length = struct.unpack('<I', length_bytes)[0]
            
            # 读取数据
            data = f.read(length)
            if not data or len(data) < length:
                break
                
            data_chunks.append(data)
    
    return data_chunks

def write_opus_file(output_path, data_chunks):
    """
    将数据写入opus文件，使用4字节长度+数据的格式
    """
    with open(output_path, 'wb') as f:
        for chunk in data_chunks:
            # 写入长度（使用小端序）
            f.write(struct.pack('<I', len(chunk)))
            # 写入数据
            f.write(chunk)

def main():
    input_file = 'test02.opus'
    output_file = 'output.opus'
    
    # 确保输入文件存在
    if not os.path.exists(input_file):
        print(f"错误：找不到输入文件 {input_file}")
        return
    
    # 读取并解析输入文件
    print(f"正在解析文件: {input_file}")
    data_chunks = read_opus_file(input_file)
    print(f"解析到 {len(data_chunks)} 个数据块")
    
    # 写入新文件
    print(f"正在写入文件: {output_file}")
    write_opus_file(output_file, data_chunks)
    print("完成！")
    
    # 验证文件大小
    original_size = os.path.getsize(input_file)
    new_size = os.path.getsize(output_file)
    print(f"原始文件大小: {original_size} 字节")
    print(f"新文件大小: {new_size} 字节")

if __name__ == '__main__':
    main()
