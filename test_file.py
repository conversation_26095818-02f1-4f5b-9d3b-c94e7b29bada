import struct
import os

# 检查test02.opus文件
if os.path.exists('test02.opus'):
    with open('test02.opus', 'rb') as f:
        data = f.read()
        print(f'File size: {len(data)} bytes')
        if len(data) >= 4:
            length_le = struct.unpack('<L', data[:4])[0]
            length_be = struct.unpack('>L', data[:4])[0]
            print(f'Data length (little endian): {length_le}')
            print(f'Data length (big endian): {length_be}')
            print(f'Actual data size after header: {len(data) - 4}')
            print(f'First 20 bytes: {data[:20].hex()}')
else:
    print('test02.opus not found')
