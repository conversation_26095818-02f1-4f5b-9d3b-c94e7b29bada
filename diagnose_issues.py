#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断Opus文件转换问题的工具
"""

import struct
import os

def diagnose_issues():
    """诊断转换问题"""
    print("=== Opus文件转换问题诊断 ===")
    
    issues_found = []
    
    # 1. 检查原始文件
    print("\n1. 检查原始文件 test02.opus:")
    if not os.path.exists('test02.opus'):
        issues_found.append("原始文件test02.opus不存在")
        print("✗ 文件不存在")
    else:
        with open('test02.opus', 'rb') as f:
            data = f.read()
        
        print(f"✓ 文件大小: {len(data)} 字节")
        
        if len(data) < 4:
            issues_found.append("原始文件太小，无法包含长度头")
        else:
            # 检查长度头
            length_le = struct.unpack('<L', data[:4])[0]
            length_be = struct.unpack('>L', data[:4])[0]
            actual_size = len(data) - 4
            
            print(f"  长度头 (小端): {length_le}")
            print(f"  长度头 (大端): {length_be}")
            print(f"  实际数据大小: {actual_size}")
            
            if length_le != actual_size and length_be != actual_size:
                issues_found.append("长度头与实际数据大小不匹配")
                print("  ⚠ 长度不匹配")
            else:
                print("  ✓ 长度匹配")
            
            # 检查Opus数据
            opus_data = data[4:]
            if len(opus_data) > 0:
                print(f"  Opus数据前8字节: {opus_data[:8].hex().upper()}")
                
                # 检查TOC字节
                toc = opus_data[0]
                config = (toc >> 3) & 0x1F
                if config > 31:
                    issues_found.append("无效的Opus配置")
                    print(f"  ⚠ 无效的Opus配置: {config}")
                else:
                    print(f"  ✓ Opus配置: {config}")
    
    # 2. 检查生成的文件
    print("\n2. 检查生成的文件 output.opus:")
    if not os.path.exists('output.opus'):
        issues_found.append("生成的文件output.opus不存在")
        print("✗ 文件不存在")
    else:
        with open('output.opus', 'rb') as f:
            data = f.read()
        
        print(f"✓ 文件大小: {len(data)} 字节")
        
        # 检查OGG标识
        if not data.startswith(b'OggS'):
            issues_found.append("生成的文件不是有效的OGG文件")
            print("✗ 不是OGG文件")
        else:
            print("✓ 是OGG文件")
            
            # 检查第一页
            if len(data) >= 27:
                header_type = data[5]
                page_segments = data[26]
                
                print(f"  第一页类型: 0x{header_type:02X}")
                print(f"  段数: {page_segments}")
                
                # 检查是否是BOS页面
                if not (header_type & 0x02):
                    issues_found.append("第一页不是流开始页面(BOS)")
                    print("  ⚠ 不是BOS页面")
                else:
                    print("  ✓ 是BOS页面")
                
                # 检查OpusHead
                if len(data) >= 27 + page_segments:
                    segment_table = data[27:27+page_segments]
                    data_size = sum(segment_table)
                    header_size = 27 + page_segments
                    
                    if len(data) >= header_size + data_size:
                        page_data = data[header_size:header_size+data_size]
                        
                        if not page_data.startswith(b'OpusHead'):
                            issues_found.append("第一页不包含OpusHead")
                            print("  ✗ 不是OpusHead")
                        else:
                            print("  ✓ 包含OpusHead")
                            
                            # 检查OpusHead结构
                            if len(page_data) >= 19:
                                version = page_data[8]
                                channels = page_data[9]
                                pre_skip = struct.unpack('<H', page_data[10:12])[0]
                                sample_rate = struct.unpack('<L', page_data[12:16])[0]
                                
                                print(f"    版本: {version}")
                                print(f"    声道: {channels}")
                                print(f"    预跳过: {pre_skip}")
                                print(f"    采样率: {sample_rate}")
                                
                                if version != 1:
                                    issues_found.append("OpusHead版本不正确")
                                if channels not in [1, 2]:
                                    issues_found.append("声道数不正确")
                                if sample_rate not in [8000, 12000, 16000, 24000, 48000]:
                                    issues_found.append("采样率可能不正确")
    
    # 3. 常见问题分析
    print("\n3. 常见问题分析:")
    
    common_issues = [
        ("CRC32校验和错误", "OGG页面的CRC32计算可能不正确"),
        ("Granule Position错误", "音频页面的granule position应该是样本数，不是字节数"),
        ("Opus数据包结构", "原始数据可能包含多个Opus包，需要正确分离"),
        ("页面分段错误", "OGG页面的段表计算可能有误"),
        ("流序列号", "所有页面应该使用相同的流序列号"),
        ("页面序列号", "页面序列号应该连续递增"),
    ]
    
    for issue, description in common_issues:
        print(f"  - {issue}: {description}")
    
    # 4. 总结
    print(f"\n4. 问题总结:")
    if issues_found:
        print("发现的问题:")
        for i, issue in enumerate(issues_found, 1):
            print(f"  {i}. {issue}")
    else:
        print("✓ 未发现明显问题")
    
    # 5. 建议的解决方案
    print(f"\n5. 建议的解决方案:")
    solutions = [
        "使用正确的CRC32算法计算OGG页面校验和",
        "确保granule position表示音频样本数而不是字节数",
        "正确解析和处理Opus数据包结构",
        "验证OGG页面的段表计算",
        "确保所有页面标志位设置正确",
        "使用标准的Opus头部参数",
    ]
    
    for i, solution in enumerate(solutions, 1):
        print(f"  {i}. {solution}")

if __name__ == "__main__":
    diagnose_issues()
    print("\n按回车键退出...")
    input()
