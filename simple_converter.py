#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Opus到OGG转换器
专门处理test02.opus文件（4字节长度+数据格式）
"""

import struct
import os

def calculate_crc32(data):
    """计算OGG CRC32校验和"""
    # 简化的CRC32表
    crc_table = []
    for i in range(256):
        crc = i << 24
        for j in range(8):
            if crc & 0x80000000:
                crc = (crc << 1) ^ 0x04C11DB7
            else:
                crc = crc << 1
            crc &= 0xFFFFFFFF
        crc_table.append(crc)
    
    crc = 0
    for byte in data:
        crc = (crc << 8) ^ crc_table[((crc >> 24) ^ byte) & 0xFF]
        crc &= 0xFFFFFFFF
    return crc

def create_ogg_page(data, page_type=0, granule=0, page_seq=0):
    """创建OGG页面"""
    # 计算段表
    segments = []
    pos = 0
    while pos < len(data):
        segment_size = min(255, len(data) - pos)
        segments.append(segment_size)
        pos += segment_size
    
    # 如果最后一段是255字节，添加0字节段表示结束
    if segments and segments[-1] == 255:
        segments.append(0)
    
    # 构建页面头（CRC先设为0）
    header = b'OggS'  # 捕获模式
    header += struct.pack('<B', 0)  # 版本
    header += struct.pack('<B', page_type)  # 页面类型
    header += struct.pack('<Q', granule)  # 颗粒位置
    header += struct.pack('<L', 1)  # 流序列号
    header += struct.pack('<L', page_seq)  # 页面序列号
    header += struct.pack('<L', 0)  # CRC（临时）
    header += struct.pack('<B', len(segments))  # 段数
    header += bytes(segments)  # 段表
    
    # 计算CRC
    full_page = header + data
    crc = calculate_crc32(full_page)
    
    # 重建页面头，包含正确的CRC
    header = b'OggS'
    header += struct.pack('<B', 0)
    header += struct.pack('<B', page_type)
    header += struct.pack('<Q', granule)
    header += struct.pack('<L', 1)
    header += struct.pack('<L', page_seq)
    header += struct.pack('<L', crc)
    header += struct.pack('<B', len(segments))
    header += bytes(segments)
    
    return header + data

def convert_opus_file():
    """转换test02.opus到标准OGG Opus格式"""
    input_file = "test02.opus"
    output_file = "output.opus"
    
    print("=== 简化版Opus转换器 ===")
    
    # 检查输入文件
    if not os.path.exists(input_file):
        print(f"错误: 找不到文件 {input_file}")
        return False
    
    try:
        # 读取输入文件
        with open(input_file, 'rb') as f:
            raw_data = f.read()
        
        print(f"输入文件大小: {len(raw_data)} 字节")
        
        if len(raw_data) < 4:
            print("错误: 文件太小")
            return False
        
        # 解析长度头
        data_length_le = struct.unpack('<L', raw_data[:4])[0]
        data_length_be = struct.unpack('>L', raw_data[:4])[0]
        opus_data = raw_data[4:]
        
        print(f"长度字段 (小端): {data_length_le}")
        print(f"长度字段 (大端): {data_length_be}")
        print(f"实际数据大小: {len(opus_data)}")
        
        # 选择正确的字节序
        if data_length_le == len(opus_data):
            data_length = data_length_le
            print("使用小端序")
        elif data_length_be == len(opus_data):
            data_length = data_length_be
            print("使用大端序")
        else:
            print("警告: 长度不匹配，使用实际大小")
            data_length = len(opus_data)
        
        # 创建Opus头部
        opus_header = b'OpusHead'
        opus_header += struct.pack('<B', 1)  # 版本
        opus_header += struct.pack('<B', 1)  # 声道数
        opus_header += struct.pack('<H', 0)  # 预跳过
        opus_header += struct.pack('<L', 48000)  # 采样率
        opus_header += struct.pack('<H', 0)  # 输出增益
        opus_header += struct.pack('<B', 0)  # 声道映射
        
        # 创建Opus注释
        vendor = b'Simple Converter'
        opus_comment = b'OpusTags'
        opus_comment += struct.pack('<L', len(vendor))
        opus_comment += vendor
        opus_comment += struct.pack('<L', 0)  # 用户注释数
        
        # 写入输出文件
        with open(output_file, 'wb') as f:
            # 第一页: Opus头部
            page1 = create_ogg_page(opus_header, page_type=2, granule=0, page_seq=0)
            f.write(page1)
            
            # 第二页: Opus注释
            page2 = create_ogg_page(opus_comment, page_type=0, granule=0, page_seq=1)
            f.write(page2)
            
            # 第三页: 音频数据
            page3 = create_ogg_page(opus_data, page_type=4, granule=data_length, page_seq=2)
            f.write(page3)
        
        output_size = os.path.getsize(output_file)
        print(f"转换成功！")
        print(f"输出文件: {output_file}")
        print(f"输出大小: {output_size} 字节")
        return True
        
    except Exception as e:
        print(f"转换失败: {e}")
        return False

if __name__ == "__main__":
    convert_opus_file()
    input("按回车键退出...")
