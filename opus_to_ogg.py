#!/usr/bin/env python3
"""
将test02.opus文件（4字节长度+数据格式）转换为带有OGG容器的标准Opus文件
"""

import struct
import os
import sys
from typing import Optional

class OggOpusConverter:
    def __init__(self):
        # OGG页面头部结构
        self.ogg_page_header = b'OggS'
        self.stream_structure_version = 0
        self.header_type_flag = 0
        self.granule_position = 0
        self.bitstream_serial_number = 1
        self.page_sequence_number = 0
        self.page_checksum = 0
        
    def crc32_ogg(self, data: bytes) -> int:
        """计算OGG CRC32校验和"""
        crc_table = [
            0x00000000, 0x04c11db7, 0x09823b6e, 0x0d4326d9,
            0x130476dc, 0x17c56b6b, 0x1a864db2, 0x1e475005,
            0x2608edb8, 0x22c9f00f, 0x2f8ad6d6, 0x2b4bcb61,
            0x350c9b64, 0x31cd86d3, 0x3c8ea00a, 0x384fbdbd,
            0x4c11db70, 0x48d0c6c7, 0x4593e01e, 0x4152fda9,
            0x5f15adac, 0x5bd4b01b, 0x569796c2, 0x52568b75,
            0x6a1936c8, 0x6ed82b7f, 0x639b0da6, 0x675a1011,
            0x791d4014, 0x7ddc5da3, 0x709f7b7a, 0x745e66cd,
            0x9823b6e0, 0x9ce2ab57, 0x91a18d8e, 0x95609039,
            0x8b27c03c, 0x8fe6dd8b, 0x82a5fb52, 0x8664e6e5,
            0xbe2b5b58, 0xbaea46ef, 0xb7a96036, 0xb3687d81,
            0xad2f2d84, 0xa9ee3033, 0xa4ad16ea, 0xa06c0b5d,
            0xd4326d90, 0xd0f37027, 0xddb056fe, 0xd9714b49,
            0xc7361b4c, 0xc3f706fb, 0xceb42022, 0xca753d95,
            0xf23a8028, 0xf6fb9d9f, 0xfbb8bb46, 0xff79a6f1,
            0xe13ef6f4, 0xe5ffeb43, 0xe8bccd9a, 0xec7dd02d,
            0x34867077, 0x30476dc0, 0x3d044b19, 0x39c556ae,
            0x278206ab, 0x23431b1c, 0x2e003dc5, 0x2ac12072,
            0x128e9dcf, 0x164f8078, 0x1b0ca6a1, 0x1fcdbb16,
            0x018aeb13, 0x054bf6a4, 0x0808d07d, 0x0cc9cdca,
            0x7897ab07, 0x7c56b6b0, 0x71159069, 0x75d48dde,
            0x6b93dddb, 0x6f52c06c, 0x6211e6b5, 0x66d0fb02,
            0x5e9f46bf, 0x5a5e5b08, 0x571d7dd1, 0x53dc6066,
            0x4d9b3063, 0x495a2dd4, 0x44190b0d, 0x40d816ba,
            0xaca5c697, 0xa864db20, 0xa527fdf9, 0xa1e6e04e,
            0xbfa1b04b, 0xbb60adfc, 0xb6238b25, 0xb2e29692,
            0x8aad2b2f, 0x8e6c3698, 0x832f1041, 0x87ee0df6,
            0x99a95df3, 0x9d684044, 0x902b669d, 0x94ea7b2a,
            0xe0b41de7, 0xe4750050, 0xe9362689, 0xedf73b3e,
            0xf3b06b3b, 0xf771768c, 0xfa325055, 0xfef34de2,
            0xc6bcf05f, 0xc27dede8, 0xcf3ecb31, 0xcbffd686,
            0xd5b88683, 0xd1799b34, 0xdc3abded, 0xd8fba05a,
            0x690ce0ee, 0x6dcdfd59, 0x608edb80, 0x644fc637,
            0x7a089632, 0x7ec98b85, 0x738aad5c, 0x774bb0eb,
            0x4f040d56, 0x4bc510e1, 0x46863638, 0x42472b8f,
            0x5c007b8a, 0x58c1663d, 0x558240e4, 0x51435d53,
            0x251d3b9e, 0x21dc2629, 0x2c9f00f0, 0x285e1d47,
            0x36194d42, 0x32d850f5, 0x3f9b762c, 0x3b5a6b9b,
            0x0315d626, 0x07d4cb91, 0x0a97ed48, 0x0e56f0ff,
            0x1011a0fa, 0x14d0bd4d, 0x19939b94, 0x1d528623,
            0xf12f560e, 0xf5ee4bb9, 0xf8ad6d60, 0xfc6c70d7,
            0xe22b20d2, 0xe6ea3d65, 0xeba91bbc, 0xef68060b,
            0xd727bbb6, 0xd3e6a601, 0xdea580d8, 0xda649d6f,
            0xc423cd6a, 0xc0e2d0dd, 0xcda1f604, 0xc960ebb3,
            0xbd3e8d7e, 0xb9ff90c9, 0xb4bcb610, 0xb07daba7,
            0xae3afba2, 0xaafbe615, 0xa7b8c0cc, 0xa379dd7b,
            0x9b3660c6, 0x9ff77d71, 0x92b45ba8, 0x9675461f,
            0x8832161a, 0x8cf30bad, 0x81b02d74, 0x857130c3,
            0x5d8a9099, 0x594b8d2e, 0x5408abf7, 0x50c9b640,
            0x4e8ee645, 0x4a4ffbf2, 0x470cdd2b, 0x43cdc09c,
            0x7b827d21, 0x7f436096, 0x7200464f, 0x76c15bf8,
            0x68860bfd, 0x6c47164a, 0x61043093, 0x65c52d24,
            0x119b4be9, 0x155a565e, 0x18197087, 0x1cd86d30,
            0x029f3d35, 0x065e2082, 0x0b1d065b, 0x0fdc1bec,
            0x3793a651, 0x3352bbe6, 0x3e119d3f, 0x3ad08088,
            0x2497d08d, 0x2056cd3a, 0x2d15ebe3, 0x29d4f654,
            0xc5a92679, 0xc1683bce, 0xcc2b1d17, 0xc8ea00a0,
            0xd6ad50a5, 0xd26c4d12, 0xdf2f6bcb, 0xdbee767c,
            0xe3a1cbc1, 0xe760d676, 0xea23f0af, 0xeee2ed18,
            0xf0a5bd1d, 0xf464a0aa, 0xf9278673, 0xfde69bc4,
            0x89b8fd09, 0x8d79e0be, 0x803ac667, 0x84fbdbd0,
            0x9abc8bd5, 0x9e7d9662, 0x933eb0bb, 0x97ffad0c,
            0xafb010b1, 0xab710d06, 0xa6322bdf, 0xa2f33668,
            0xbcb4666d, 0xb8757bda, 0xb5365d03, 0xb1f740b4
        ]
        
        crc = 0
        for byte in data:
            crc = (crc << 8) ^ crc_table[((crc >> 24) ^ byte) & 0xff]
            crc &= 0xffffffff
        return crc

    def create_opus_header(self, sample_rate: int = 48000, channels: int = 1) -> bytes:
        """创建Opus头部"""
        header = b'OpusHead'
        header += struct.pack('<B', 1)  # Version
        header += struct.pack('<B', channels)  # Channel count
        header += struct.pack('<H', 0)  # Pre-skip (samples at 48kHz)
        header += struct.pack('<L', sample_rate)  # Original sample rate
        header += struct.pack('<H', 0)  # Output gain
        header += struct.pack('<B', 0)  # Channel mapping family
        return header

    def create_opus_comment(self) -> bytes:
        """创建Opus注释头"""
        vendor_string = b'Python Opus Converter'
        comment = b'OpusTags'
        comment += struct.pack('<L', len(vendor_string))
        comment += vendor_string
        comment += struct.pack('<L', 0)  # User comment list length
        return comment

    def create_ogg_page(self, data: bytes, header_type: int = 0, granule_pos: int = 0, 
                       page_seq: int = 0, is_first: bool = False, is_last: bool = False) -> bytes:
        """创建OGG页面"""
        if is_first:
            header_type |= 0x02  # Beginning of stream
        if is_last:
            header_type |= 0x04  # End of stream
            
        # 计算段表
        segments = []
        remaining = len(data)
        while remaining > 0:
            segment_size = min(255, remaining)
            segments.append(segment_size)
            remaining -= segment_size
            
        # 如果最后一个段是255字节，需要添加一个0字节段来表示页面结束
        if segments and segments[-1] == 255:
            segments.append(0)
            
        segment_table = bytes(segments)
        
        # 构建页面头（不包含CRC）
        page_header = b'OggS'
        page_header += struct.pack('<B', 0)  # Stream structure version
        page_header += struct.pack('<B', header_type)  # Header type flag
        page_header += struct.pack('<Q', granule_pos)  # Granule position
        page_header += struct.pack('<L', 1)  # Bitstream serial number
        page_header += struct.pack('<L', page_seq)  # Page sequence number
        page_header += struct.pack('<L', 0)  # CRC checksum (临时设为0)
        page_header += struct.pack('<B', len(segments))  # Page segments
        page_header += segment_table
        
        # 计算CRC
        full_page = page_header + data
        crc = self.crc32_ogg(full_page)
        
        # 重新构建页面头，这次包含正确的CRC
        page_header = b'OggS'
        page_header += struct.pack('<B', 0)  # Stream structure version
        page_header += struct.pack('<B', header_type)  # Header type flag
        page_header += struct.pack('<Q', granule_pos)  # Granule position
        page_header += struct.pack('<L', 1)  # Bitstream serial number
        page_header += struct.pack('<L', page_seq)  # Page sequence number
        page_header += struct.pack('<L', crc)  # CRC checksum
        page_header += struct.pack('<B', len(segments))  # Page segments
        page_header += segment_table
        
        return page_header + data

    def convert_to_ogg_opus(self, input_file: str, output_file: str) -> bool:
        """将test02.opus转换为OGG Opus文件"""
        try:
            # 读取输入文件
            with open(input_file, 'rb') as f:
                data = f.read()
                
            if len(data) < 4:
                print(f"错误: 文件 {input_file} 太小，无法包含长度头")
                return False
                
            # 读取数据长度（假设是小端序）
            data_length = struct.unpack('<L', data[:4])[0]
            opus_data = data[4:]
            
            print(f"文件大小: {len(data)} 字节")
            print(f"数据长度字段: {data_length}")
            print(f"实际Opus数据大小: {len(opus_data)}")
            
            if len(opus_data) != data_length:
                print(f"警告: 数据长度不匹配，尝试使用大端序...")
                data_length = struct.unpack('>L', data[:4])[0]
                if len(opus_data) != data_length:
                    print(f"警告: 数据长度仍不匹配，使用实际数据大小")
                    data_length = len(opus_data)
            
            # 创建OGG Opus文件
            with open(output_file, 'wb') as f:
                # 第一页: Opus头
                opus_header = self.create_opus_header()
                page1 = self.create_ogg_page(opus_header, header_type=0, granule_pos=0, 
                                           page_seq=0, is_first=True)
                f.write(page1)
                
                # 第二页: Opus注释
                opus_comment = self.create_opus_comment()
                page2 = self.create_ogg_page(opus_comment, header_type=0, granule_pos=0, 
                                           page_seq=1)
                f.write(page2)
                
                # 第三页及后续: Opus音频数据
                # 这里我们假设整个opus_data是一个完整的音频流
                # 实际应用中可能需要解析Opus包来确定正确的granule position
                page3 = self.create_ogg_page(opus_data, header_type=0, granule_pos=data_length, 
                                           page_seq=2, is_last=True)
                f.write(page3)
                
            print(f"成功转换: {input_file} -> {output_file}")
            return True
            
        except Exception as e:
            print(f"转换失败: {e}")
            return False

def main():
    converter = OggOpusConverter()

    input_file = "test02.opus"
    output_file = "output.opus"

    print("=== Opus to OGG Opus 转换器 ===")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")

    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
        print("请确保test02.opus文件在当前目录中")
        return

    # 先检查文件内容
    try:
        with open(input_file, 'rb') as f:
            data = f.read()
            print(f"\n文件信息:")
            print(f"- 总大小: {len(data)} 字节")
            if len(data) >= 4:
                length_le = struct.unpack('<L', data[:4])[0]
                length_be = struct.unpack('>L', data[:4])[0]
                print(f"- 长度字段 (小端序): {length_le}")
                print(f"- 长度字段 (大端序): {length_be}")
                print(f"- 数据部分大小: {len(data) - 4}")
                print(f"- 前16字节: {data[:16].hex().upper()}")
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return

    success = converter.convert_to_ogg_opus(input_file, output_file)

    if success:
        print(f"\n✓ 转换完成！输出文件: {output_file}")
        print("现在可以用音频播放器播放这个文件了。")

        # 显示输出文件信息
        if os.path.exists(output_file):
            output_size = os.path.getsize(output_file)
            print(f"输出文件大小: {output_size} 字节")
    else:
        print("✗ 转换失败！")

if __name__ == "__main__":
    main()
