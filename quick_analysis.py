import struct
import os

def analyze_files():
    print("=== 快速文件分析 ===")
    
    # 分析test02.opus
    if os.path.exists('test02.opus'):
        with open('test02.opus', 'rb') as f:
            data = f.read()
        
        print(f"\ntest02.opus:")
        print(f"- 大小: {len(data)} 字节")
        print(f"- 前16字节: {data[:16].hex().upper()}")
        
        if len(data) >= 4:
            length_le = struct.unpack('<L', data[:4])[0]
            length_be = struct.unpack('>L', data[:4])[0]
            print(f"- 长度字段 (小端): {length_le}")
            print(f"- 长度字段 (大端): {length_be}")
            print(f"- 数据部分: {len(data)-4} 字节")
            
            opus_data = data[4:]
            if len(opus_data) > 0:
                print(f"- Opus数据前8字节: {opus_data[:8].hex().upper()}")
    
    # 分析output.opus
    if os.path.exists('output.opus'):
        with open('output.opus', 'rb') as f:
            data = f.read()
        
        print(f"\noutput.opus:")
        print(f"- 大小: {len(data)} 字节")
        print(f"- 前16字节: {data[:16].hex().upper()}")
        
        # 检查是否是OGG文件
        if data.startswith(b'OggS'):
            print("- ✓ 是OGG文件")
            
            # 分析第一页
            if len(data) >= 27:
                page_segments = data[26]
                print(f"- 第一页段数: {page_segments}")
                
                if len(data) >= 27 + page_segments:
                    segment_table = data[27:27+page_segments]
                    data_size = sum(segment_table)
                    header_size = 27 + page_segments
                    
                    print(f"- 第一页头部大小: {header_size}")
                    print(f"- 第一页数据大小: {data_size}")
                    
                    if header_size + data_size <= len(data):
                        page_data = data[header_size:header_size+data_size]
                        print(f"- 第一页数据: {page_data[:16].hex().upper()}")
                        
                        if page_data.startswith(b'OpusHead'):
                            print("- ✓ 第一页是OpusHead")
                        else:
                            print("- ✗ 第一页不是OpusHead")
        else:
            print("- ✗ 不是OGG文件")
    
    # 分析test.opus（如果存在）
    if os.path.exists('test.opus'):
        with open('test.opus', 'rb') as f:
            data = f.read()
        
        print(f"\ntest.opus:")
        print(f"- 大小: {len(data)} 字节")
        print(f"- 前16字节: {data[:16].hex().upper()}")
        
        if data.startswith(b'OggS'):
            print("- ✓ 是OGG文件")
        else:
            print("- ✗ 不是OGG文件")

if __name__ == "__main__":
    analyze_files()
