#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版Opus到OGG转换器
解决生成文件无法播放的问题
"""

import struct
import os

def calculate_ogg_crc32(data):
    """正确的OGG CRC32计算"""
    # 标准OGG CRC32多项式: 0x04C11DB7
    crc_table = []
    for i in range(256):
        crc = i << 24
        for _ in range(8):
            if crc & 0x80000000:
                crc = (crc << 1) ^ 0x04C11DB7
            else:
                crc = crc << 1
            crc &= 0xFFFFFFFF
        crc_table.append(crc)
    
    crc = 0
    for byte in data:
        crc = (crc << 8) ^ crc_table[((crc >> 24) ^ byte) & 0xFF]
        crc &= 0xFFFFFFFF
    return crc

def create_ogg_page(data, page_type=0, granule=0, page_seq=0, stream_serial=1):
    """创建OGG页面，修复CRC计算"""
    # 计算段表
    segments = []
    pos = 0
    while pos < len(data):
        segment_size = min(255, len(data) - pos)
        segments.append(segment_size)
        pos += segment_size
    
    # 如果最后一段是255字节，添加0字节段表示结束
    if segments and segments[-1] == 255:
        segments.append(0)
    
    segment_table = bytes(segments)
    
    # 构建页面头（CRC先设为0）
    header = b'OggS'  # 捕获模式
    header += struct.pack('<B', 0)  # 版本
    header += struct.pack('<B', page_type)  # 页面类型
    header += struct.pack('<Q', granule)  # 颗粒位置
    header += struct.pack('<L', stream_serial)  # 流序列号
    header += struct.pack('<L', page_seq)  # 页面序列号
    header += struct.pack('<L', 0)  # CRC（临时设为0）
    header += struct.pack('<B', len(segments))  # 段数
    header += segment_table  # 段表
    
    # 计算CRC（包含整个页面）
    full_page = header + data
    crc = calculate_ogg_crc32(full_page)
    
    # 重建页面头，包含正确的CRC
    header = b'OggS'
    header += struct.pack('<B', 0)
    header += struct.pack('<B', page_type)
    header += struct.pack('<Q', granule)
    header += struct.pack('<L', stream_serial)
    header += struct.pack('<L', page_seq)
    header += struct.pack('<L', crc)
    header += struct.pack('<B', len(segments))
    header += segment_table
    
    return header + data

def parse_opus_packets(data):
    """解析Opus数据，尝试分离多个包"""
    packets = []
    pos = 0
    
    while pos < len(data):
        # 查找可能的Opus包开始
        # Opus包通常以特定的TOC字节开始
        if pos + 1 >= len(data):
            break
            
        # 简单的启发式方法：查找重复的模式
        # 实际应用中需要更复杂的Opus包解析
        
        # 假设每个包都以相似的模式开始
        packet_start = pos
        
        # 查找下一个可能的包开始位置
        next_pos = pos + 1
        while next_pos < len(data) - 1:
            # 查找类似的TOC字节模式
            if (data[next_pos] & 0xF8) == (data[pos] & 0xF8):
                break
            next_pos += 1
        
        if next_pos >= len(data):
            # 这是最后一个包
            packets.append(data[packet_start:])
            break
        else:
            packets.append(data[packet_start:next_pos])
            pos = next_pos
    
    return packets if packets else [data]  # 如果解析失败，返回整个数据作为单个包

def estimate_samples_from_opus_data(data, sample_rate=48000):
    """估算Opus数据的样本数"""
    # 这是一个简化的估算方法
    # 实际应用中需要解析Opus包头来获取准确的样本数
    
    # Opus的典型帧长度（毫秒）
    typical_frame_lengths = [2.5, 5, 10, 20, 40, 60]
    
    # 假设使用20ms帧（最常见）
    frame_length_ms = 20
    samples_per_frame = int(sample_rate * frame_length_ms / 1000)
    
    # 估算包数（这是一个粗略的估算）
    estimated_packets = max(1, len(data) // 50)  # 假设平均每个包50字节
    
    return estimated_packets * samples_per_frame

def convert_opus_file():
    """修复版转换函数"""
    input_file = "test02.opus"
    output_file = "output_fixed.opus"
    
    print("=== 修复版Opus转换器 ===")
    
    # 检查输入文件
    if not os.path.exists(input_file):
        print(f"错误: 找不到文件 {input_file}")
        return False
    
    try:
        # 读取输入文件
        with open(input_file, 'rb') as f:
            raw_data = f.read()
        
        print(f"输入文件大小: {len(raw_data)} 字节")
        
        if len(raw_data) < 4:
            print("错误: 文件太小")
            return False
        
        # 解析长度头
        data_length_le = struct.unpack('<L', raw_data[:4])[0]
        data_length_be = struct.unpack('>L', raw_data[:4])[0]
        opus_data = raw_data[4:]
        
        print(f"长度字段 (小端): {data_length_le}")
        print(f"长度字段 (大端): {data_length_be}")
        print(f"实际数据大小: {len(opus_data)}")
        
        # 选择正确的字节序
        if data_length_le == len(opus_data):
            data_length = data_length_le
            print("使用小端序")
        elif data_length_be == len(opus_data):
            data_length = data_length_be
            print("使用大端序")
        else:
            print("警告: 长度不匹配，使用实际大小")
            data_length = len(opus_data)
        
        # 显示Opus数据的前几个字节用于调试
        print(f"Opus数据前16字节: {opus_data[:16].hex().upper()}")
        
        # 分析第一个字节（TOC）
        if len(opus_data) > 0:
            toc = opus_data[0]
            config = (toc >> 3) & 0x1F
            stereo = (toc >> 2) & 0x01
            frame_count = toc & 0x03
            print(f"TOC分析: 配置={config}, 立体声={stereo}, 帧数码={frame_count}")
        
        # 创建正确的Opus头部
        opus_header = b'OpusHead'
        opus_header += struct.pack('<B', 1)  # 版本
        opus_header += struct.pack('<B', 1 if not stereo else 2)  # 声道数（根据TOC调整）
        opus_header += struct.pack('<H', 312)  # 预跳过（标准值）
        opus_header += struct.pack('<L', 48000)  # 原始采样率
        opus_header += struct.pack('<H', 0)  # 输出增益
        opus_header += struct.pack('<B', 0)  # 声道映射族
        
        # 创建Opus注释
        vendor = b'Fixed Converter v1.0'
        opus_comment = b'OpusTags'
        opus_comment += struct.pack('<L', len(vendor))
        opus_comment += vendor
        opus_comment += struct.pack('<L', 0)  # 用户注释数
        
        # 估算总样本数
        total_samples = estimate_samples_from_opus_data(opus_data)
        print(f"估算总样本数: {total_samples}")
        
        # 写入输出文件
        with open(output_file, 'wb') as f:
            # 第一页: Opus头部（BOS - Beginning of Stream）
            page1 = create_ogg_page(opus_header, page_type=2, granule=0, page_seq=0)
            f.write(page1)
            print(f"写入页面1: OpusHead ({len(page1)} 字节)")
            
            # 第二页: Opus注释
            page2 = create_ogg_page(opus_comment, page_type=0, granule=0, page_seq=1)
            f.write(page2)
            print(f"写入页面2: OpusTags ({len(page2)} 字节)")
            
            # 第三页: 音频数据（EOS - End of Stream）
            # 使用正确的granule position（样本数而不是字节数）
            page3 = create_ogg_page(opus_data, page_type=4, granule=total_samples, page_seq=2)
            f.write(page3)
            print(f"写入页面3: 音频数据 ({len(page3)} 字节)")
        
        output_size = os.path.getsize(output_file)
        print(f"\n✓ 转换成功！")
        print(f"输出文件: {output_file}")
        print(f"输出大小: {output_size} 字节")
        
        # 验证输出文件
        print(f"\n验证输出文件:")
        with open(output_file, 'rb') as f:
            verify_data = f.read(32)
            print(f"前32字节: {verify_data.hex().upper()}")
            if verify_data.startswith(b'OggS'):
                print("✓ 输出文件是有效的OGG文件")
            else:
                print("✗ 输出文件格式错误")
        
        return True
        
    except Exception as e:
        print(f"转换失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    convert_opus_file()
    print("\n按回车键退出...")
    input()
