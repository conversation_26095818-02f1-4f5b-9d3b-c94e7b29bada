#!/usr/bin/env python3

import struct
import os

def main():
    print("=== 直接文件分析 ===")
    
    # 分析test02.opus
    print("\n1. 分析 test02.opus:")
    try:
        with open('test02.opus', 'rb') as f:
            data = f.read()
        
        print(f"文件大小: {len(data)} 字节")
        
        # 显示前32字节的十六进制
        hex_str = ' '.join(f'{b:02X}' for b in data[:32])
        print(f"前32字节: {hex_str}")
        
        # 分析长度头
        if len(data) >= 4:
            length_le = struct.unpack('<L', data[:4])[0]
            length_be = struct.unpack('>L', data[:4])[0]
            print(f"长度字段 (小端序): {length_le}")
            print(f"长度字段 (大端序): {length_be}")
            print(f"实际数据大小: {len(data) - 4}")
            
            # 检查哪个字节序正确
            if length_le == len(data) - 4:
                print("✓ 小端序匹配")
            elif length_be == len(data) - 4:
                print("✓ 大端序匹配")
            else:
                print("⚠ 长度不匹配")
            
            # 显示Opus数据的前16字节
            opus_data = data[4:]
            if len(opus_data) >= 16:
                opus_hex = ' '.join(f'{b:02X}' for b in opus_data[:16])
                print(f"Opus数据前16字节: {opus_hex}")
                
                # 分析第一个字节（TOC字节）
                toc = opus_data[0]
                config = (toc >> 3) & 0x1F
                stereo = (toc >> 2) & 0x01
                frame_count = toc & 0x03
                print(f"TOC字节: 0x{toc:02X}")
                print(f"  配置: {config}")
                print(f"  立体声: {stereo}")
                print(f"  帧计数码: {frame_count}")
                
    except Exception as e:
        print(f"读取test02.opus失败: {e}")
    
    # 分析output.opus
    print("\n2. 分析 output.opus:")
    try:
        with open('output.opus', 'rb') as f:
            data = f.read()
        
        print(f"文件大小: {len(data)} 字节")
        
        # 显示前32字节的十六进制
        hex_str = ' '.join(f'{b:02X}' for b in data[:32])
        print(f"前32字节: {hex_str}")
        
        # 检查OGG标识
        if data[:4] == b'OggS':
            print("✓ 是有效的OGG文件")
            
            # 分析第一页
            if len(data) >= 27:
                version = data[4]
                header_type = data[5]
                granule_pos = struct.unpack('<Q', data[6:14])[0]
                serial_num = struct.unpack('<L', data[14:18])[0]
                page_seq = struct.unpack('<L', data[18:22])[0]
                checksum = struct.unpack('<L', data[22:26])[0]
                page_segments = data[26]
                
                print(f"OGG页面信息:")
                print(f"  版本: {version}")
                print(f"  头部类型: 0x{header_type:02X}")
                print(f"  颗粒位置: {granule_pos}")
                print(f"  流序列号: {serial_num}")
                print(f"  页面序列号: {page_seq}")
                print(f"  校验和: 0x{checksum:08X}")
                print(f"  段数: {page_segments}")
                
                # 读取段表
                if len(data) >= 27 + page_segments:
                    segment_table = data[27:27+page_segments]
                    data_size = sum(segment_table)
                    header_size = 27 + page_segments
                    
                    print(f"  段表: {list(segment_table)}")
                    print(f"  头部大小: {header_size}")
                    print(f"  数据大小: {data_size}")
                    
                    # 检查页面数据
                    if len(data) >= header_size + data_size:
                        page_data = data[header_size:header_size+data_size]
                        page_hex = ' '.join(f'{b:02X}' for b in page_data[:16])
                        print(f"  页面数据前16字节: {page_hex}")
                        
                        if page_data.startswith(b'OpusHead'):
                            print("  ✓ 第一页是OpusHead")
                            
                            # 分析OpusHead结构
                            if len(page_data) >= 19:
                                opus_version = page_data[8]
                                channels = page_data[9]
                                pre_skip = struct.unpack('<H', page_data[10:12])[0]
                                sample_rate = struct.unpack('<L', page_data[12:16])[0]
                                gain = struct.unpack('<h', page_data[16:18])[0]
                                mapping_family = page_data[18]
                                
                                print(f"    Opus版本: {opus_version}")
                                print(f"    声道数: {channels}")
                                print(f"    预跳过: {pre_skip}")
                                print(f"    采样率: {sample_rate}")
                                print(f"    增益: {gain}")
                                print(f"    映射族: {mapping_family}")
                        else:
                            print("  ✗ 第一页不是OpusHead")
        else:
            print("✗ 不是有效的OGG文件")
            
    except Exception as e:
        print(f"读取output.opus失败: {e}")
    
    # 比较test.opus（如果存在）
    if os.path.exists('test.opus'):
        print("\n3. 分析 test.opus (参考文件):")
        try:
            with open('test.opus', 'rb') as f:
                data = f.read()
            
            print(f"文件大小: {len(data)} 字节")
            hex_str = ' '.join(f'{b:02X}' for b in data[:32])
            print(f"前32字节: {hex_str}")
            
            if data[:4] == b'OggS':
                print("✓ 是有效的OGG文件")
            else:
                print("✗ 不是有效的OGG文件")
                
        except Exception as e:
            print(f"读取test.opus失败: {e}")

if __name__ == "__main__":
    main()
    print("\n按回车键退出...")
    input()
