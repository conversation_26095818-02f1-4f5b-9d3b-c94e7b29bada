#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析test02.opus和output.opus文件的详细结构
找出为什么生成的文件无法播放的原因
"""

import struct
import os

def hex_dump(data, offset=0, length=None):
    """十六进制转储"""
    if length is None:
        length = len(data)
    
    lines = []
    for i in range(0, min(length, len(data)), 16):
        hex_part = ' '.join(f'{b:02X}' for b in data[i:i+16])
        ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data[i:i+16])
        lines.append(f'{offset+i:08X}: {hex_part:<48} {ascii_part}')
    return '\n'.join(lines)

def analyze_opus_packet(data):
    """分析Opus数据包结构"""
    if len(data) == 0:
        return "空数据包"
    
    # Opus包的第一个字节包含配置信息
    config_byte = data[0]
    
    # TOC (Table of Contents) 字节分析
    config = (config_byte >> 3) & 0x1F  # 配置号 (5位)
    stereo = (config_byte >> 2) & 0x01  # 立体声标志
    frame_count_code = config_byte & 0x03  # 帧数编码
    
    # 根据配置号确定模式
    if config < 12:
        mode = "SILK-only"
        bandwidth = ["NB", "MB", "WB", "SWB", "FB"][min(config // 4, 4)]
    elif config < 16:
        mode = "Hybrid"
        bandwidth = ["SWB", "FB"][(config - 12) // 2]
    else:
        mode = "CELT-only"
        bandwidth = ["NB", "WB", "SWB", "FB"][min((config - 16) // 4, 3)]
    
    # 帧数分析
    if frame_count_code == 0:
        frame_count = 1
    elif frame_count_code == 1:
        frame_count = 2
    elif frame_count_code == 2:
        frame_count = 2  # VBR
    else:
        frame_count = "多帧"
    
    return f"模式: {mode}, 带宽: {bandwidth}, 声道: {'立体声' if stereo else '单声道'}, 帧数: {frame_count}"

def analyze_ogg_page(data, offset=0):
    """分析OGG页面结构"""
    if len(data) < 27:
        return None, "页面太小"
    
    # 检查OGG标识
    if data[offset:offset+4] != b'OggS':
        return None, "不是有效的OGG页面"
    
    # 解析页面头
    version = data[offset+4]
    header_type = data[offset+5]
    granule_pos = struct.unpack('<Q', data[offset+6:offset+14])[0]
    serial_num = struct.unpack('<L', data[offset+14:offset+18])[0]
    page_seq = struct.unpack('<L', data[offset+18:offset+22])[0]
    checksum = struct.unpack('<L', data[offset+22:offset+26])[0]
    page_segments = data[offset+26]
    
    # 读取段表
    segment_table = data[offset+27:offset+27+page_segments]
    header_size = 27 + page_segments
    
    # 计算页面数据大小
    page_data_size = sum(segment_table)
    
    # 页面类型分析
    type_flags = []
    if header_type & 0x01:
        type_flags.append("继续包")
    if header_type & 0x02:
        type_flags.append("流开始")
    if header_type & 0x04:
        type_flags.append("流结束")
    
    page_info = {
        'version': version,
        'header_type': header_type,
        'type_flags': type_flags,
        'granule_pos': granule_pos,
        'serial_num': serial_num,
        'page_seq': page_seq,
        'checksum': checksum,
        'page_segments': page_segments,
        'segment_table': list(segment_table),
        'header_size': header_size,
        'page_data_size': page_data_size,
        'total_size': header_size + page_data_size
    }
    
    return page_info, None

def analyze_test02_opus():
    """分析test02.opus文件"""
    print("=== 分析 test02.opus ===")
    
    if not os.path.exists('test02.opus'):
        print("文件不存在")
        return
    
    with open('test02.opus', 'rb') as f:
        data = f.read()
    
    print(f"文件大小: {len(data)} 字节")
    
    if len(data) < 4:
        print("文件太小")
        return
    
    # 分析长度头
    length_le = struct.unpack('<L', data[:4])[0]
    length_be = struct.unpack('>L', data[:4])[0]
    opus_data = data[4:]
    
    print(f"长度字段 (小端序): {length_le}")
    print(f"长度字段 (大端序): {length_be}")
    print(f"实际数据大小: {len(opus_data)}")
    
    # 确定正确的字节序
    if length_le == len(opus_data):
        print("✓ 使用小端序")
        data_length = length_le
    elif length_be == len(opus_data):
        print("✓ 使用大端序")
        data_length = length_be
    else:
        print("⚠ 长度不匹配")
        data_length = len(opus_data)
    
    print(f"\n前64字节十六进制转储:")
    print(hex_dump(data, 0, 64))
    
    print(f"\nOpus数据前32字节:")
    print(hex_dump(opus_data, 0, 32))
    
    # 分析Opus数据包
    if len(opus_data) > 0:
        print(f"\nOpus包分析:")
        print(analyze_opus_packet(opus_data))

def analyze_output_opus():
    """分析output.opus文件"""
    print("\n=== 分析 output.opus ===")
    
    if not os.path.exists('output.opus'):
        print("文件不存在")
        return
    
    with open('output.opus', 'rb') as f:
        data = f.read()
    
    print(f"文件大小: {len(data)} 字节")
    
    # 分析OGG页面
    offset = 0
    page_num = 1
    
    while offset < len(data):
        print(f"\n--- 页面 {page_num} (偏移: {offset}) ---")
        
        page_info, error = analyze_ogg_page(data, offset)
        if error:
            print(f"错误: {error}")
            break
        
        print(f"版本: {page_info['version']}")
        print(f"头部类型: 0x{page_info['header_type']:02X} ({', '.join(page_info['type_flags']) if page_info['type_flags'] else '普通页面'})")
        print(f"颗粒位置: {page_info['granule_pos']}")
        print(f"流序列号: {page_info['serial_num']}")
        print(f"页面序列号: {page_info['page_seq']}")
        print(f"校验和: 0x{page_info['checksum']:08X}")
        print(f"段数: {page_info['page_segments']}")
        print(f"段表: {page_info['segment_table']}")
        print(f"头部大小: {page_info['header_size']} 字节")
        print(f"数据大小: {page_info['page_data_size']} 字节")
        
        # 显示页面数据的前32字节
        data_start = offset + page_info['header_size']
        data_end = data_start + page_info['page_data_size']
        page_data = data[data_start:data_end]
        
        print(f"页面数据前32字节:")
        print(hex_dump(page_data, 0, 32))
        
        # 如果是Opus头部或注释，进行特殊分析
        if page_data.startswith(b'OpusHead'):
            print("✓ 这是Opus头部页面")
            if len(page_data) >= 19:
                version = page_data[8]
                channels = page_data[9]
                pre_skip = struct.unpack('<H', page_data[10:12])[0]
                sample_rate = struct.unpack('<L', page_data[12:16])[0]
                gain = struct.unpack('<h', page_data[16:18])[0]
                mapping_family = page_data[18]
                print(f"  版本: {version}")
                print(f"  声道数: {channels}")
                print(f"  预跳过: {pre_skip}")
                print(f"  采样率: {sample_rate}")
                print(f"  增益: {gain}")
                print(f"  映射族: {mapping_family}")
        elif page_data.startswith(b'OpusTags'):
            print("✓ 这是Opus注释页面")
        else:
            print("这是音频数据页面")
            if len(page_data) > 0:
                print(f"Opus包分析: {analyze_opus_packet(page_data)}")
        
        offset += page_info['total_size']
        page_num += 1
        
        if page_num > 10:  # 防止无限循环
            print("... (限制显示前10页)")
            break

def verify_crc32():
    """验证生成文件的CRC32校验和"""
    print("\n=== 验证CRC32校验和 ===")
    
    if not os.path.exists('output.opus'):
        print("output.opus文件不存在")
        return
    
    # 这里可以添加CRC32验证逻辑
    print("CRC32验证功能待实现")

def main():
    print("=== Opus文件分析工具 ===")
    
    analyze_test02_opus()
    analyze_output_opus()
    verify_crc32()
    
    print("\n=== 分析完成 ===")

if __name__ == "__main__":
    main()
