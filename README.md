# Opus to OGG Opus 转换器

这个工具可以将特殊格式的Opus文件（4字节长度头 + 数据）转换为标准的OGG Opus文件，使其能被音频播放器正确解析和播放。

## 文件说明

- `test02.opus` - 输入文件（4字节长度 + Opus数据）
- `opus_to_ogg.py` - 主要的转换脚本
- `convert.bat` - Windows批处理文件，方便运行转换
- `output.opus` - 转换后的标准OGG Opus文件

## 使用方法

### 方法1: 使用批处理文件（推荐）
1. 确保`test02.opus`文件在当前目录中
2. 双击运行`convert.bat`
3. 转换完成后会生成`output.opus`文件

### 方法2: 直接运行Python脚本
```bash
python opus_to_ogg.py
```

## 输入文件格式

输入文件`test02.opus`的格式应该是：
```
[4字节长度][Opus数据]
```

- 前4字节：数据长度（小端序或大端序）
- 后续字节：实际的Opus音频数据

## 输出文件格式

输出文件`output.opus`是标准的OGG Opus文件，包含：
1. OGG页面头
2. Opus头部信息
3. Opus注释信息
4. Opus音频数据

## 技术细节

转换器会：
1. 读取输入文件的4字节长度头
2. 提取Opus音频数据
3. 创建标准的OGG容器
4. 添加必要的Opus头部和注释
5. 计算正确的CRC校验和
6. 生成可播放的OGG Opus文件

## 系统要求

- Windows操作系统
- Python 3.6或更高版本
- 无需额外的Python库（仅使用标准库）

## 故障排除

如果转换失败，请检查：
1. Python是否正确安装
2. `test02.opus`文件是否存在
3. 文件是否有读取权限
4. 磁盘空间是否足够

## 支持的播放器

转换后的文件可以在以下播放器中播放：
- VLC Media Player
- Windows Media Player（需要编解码器）
- Chrome/Firefox浏览器
- 大多数支持Opus格式的音频播放器
